import React from "react";

export default function page() {
  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 lg:mb-16">
          <h1 className="text-2xl font-bold text-gray-900">DRIPLY</h1>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row  justify-center gap-8 lg:gap-16 items-center lg:items-start">
          {/* Left Card */}
          <div className="flex-1 max-w-lg bg-white ">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                  Links Don't Sell.
                  <br />
                  <span className="text-gray-900">Smart Conversations Do</span>
                </h2>

                <p className="text-gray-600 text-lg leading-relaxed">
                  Driply turns your Instagram bio link into an AI assistant that
                  converts followers into customers. Get personalized
                  recommendations that drive real results for your business.
                </p>
              </div>

              <button className="bg-[#DCFB41] text-black font-semibold px-8 py-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Learn More
              </button>
            </div>
          </div>

          {/* Right Card - Phone Mockups */}
          <div className="flex-1 max-w-2xl relative bg-gray-100">
            <div className="relative flex items-start justify-end">
              {/* Instagram Profile Phone (Behind) */}
              <div className="bg-black rounded-3xl p-2 shadow-2xl absolute left-28 top-8 z-10">
                <div className="bg-black rounded-2xl overflow-hidden w-64">
                  {/* Status Bar */}
                  <div className="text-white px-4 py-2 text-sm flex justify-between items-center">
                    <span>9:41</span>
                    <div className="flex gap-1 items-center">
                      <div className="w-4 h-2 bg-white rounded-sm"></div>
                      <div className="w-1 h-2 bg-white rounded-sm"></div>
                      <div className="w-6 h-2 bg-white rounded-sm"></div>
                    </div>
                  </div>

                  {/* Instagram Profile */}
                  <div className="p-4 space-y-4">
                    {/* Header */}
                    <div className="flex items-center gap-3 text-white">
                      <button className="text-lg">←</button>
                      <h3 className="font-semibold">The.Curly.Yogini</h3>
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center ml-1">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>

                    {/* Profile Section */}
                    <div className="flex items-center gap-4">
                      <div className="relative">
                        <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-gradient-to-r from-pink-500 to-yellow-500 p-1">
                          <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                            <span className="text-white text-lg font-bold">
                              CY
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex-1">
                        <div className="flex justify-between text-center text-white">
                          <div>
                            <div className="font-bold">657</div>
                            <div className="text-xs text-gray-300">Posts</div>
                          </div>
                          <div>
                            <div className="font-bold">10.2K</div>
                            <div className="text-xs text-gray-300">
                              Followers
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Name */}
                    <div className="text-white">
                      <div className="font-semibold">The.Curly.Yogini</div>
                    </div>

                    {/* Bio Link */}
                    <div className="bg-gray-800 rounded-full px-4 py-2 text-center">
                      <span className="text-blue-400 text-sm">
                        driply.chat/thecurlyyogini
                      </span>
                    </div>

                    {/* Follow Button */}
                    <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2.5 rounded-lg transition-colors">
                      Follow
                    </button>
                  </div>
                </div>
              </div>

              {/* Chat Interface Phone (Front) */}
              <div className="bg-black rounded-3xl p-2 shadow-2xl z-20">
                <div className="bg-gradient-to-b from-purple-200 to-purple-300 rounded-2xl overflow-hidden w-72">
                  {/* Chat Header */}
                  <div className="p-4 space-y-4">
                    <div className="flex items-center justify-center">
                      <div className="w-16 h-16 rounded-full overflow-hidden">
                        <div className="w-full h-full bg-gradient-to-br from-amber-400 to-orange-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg font-bold">
                            CY
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="text-center">
                      <h3 className="font-semibold text-gray-800">
                        The Curly Yogini
                      </h3>
                    </div>

                    {/* Main Message */}
                    <div className="text-center space-y-2">
                      <h2 className="text-xl font-bold text-gray-800">
                        Let's start stretching!
                      </h2>
                      <p className="text-gray-600">Lessons? Times?</p>
                      <p className="text-gray-600">Or anything else...</p>
                    </div>

                    {/* Quick Actions - Horizontal Layout */}
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <div className="flex-1 bg-white/60 rounded-2xl p-3">
                          <div className="font-semibold text-gray-800 text-sm">
                            Lessons
                          </div>
                          <div className="text-xs text-gray-600">
                            All lessons type
                          </div>
                        </div>
                        <div className="flex-1 bg-white/60 rounded-2xl p-3">
                          <div className="font-semibold text-gray-800 text-sm">
                            Morning routine course
                          </div>
                          <div className="text-xs text-gray-600">
                            10% off until today!
                          </div>
                        </div>
                      </div>

                      {/* Input Box with Button Inside */}
                      <div className="bg-white/60 rounded-2xl p-4 relative">
                        <div className="text-gray-600 text-sm mb-2">
                          Ask anything
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex-1"></div>
                          <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                            <span className="text-white text-sm">→</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
